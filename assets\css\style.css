/* Flori Construction Ltd - Main Stylesheet */

/* CSS Variables */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #e67e22;
    --accent-color: #3498db;
    --dark-color: #1a252f;
    --light-color: #ecf0f1;
    --white-color: #ffffff;
    --gray-color: #7f8c8d;
    --light-gray: #bdc3c7;

    /* Modern Design Colors */
    --modern-dark: #1a1a1a;
    --modern-light: #f8f9fa;
    --modern-accent: #007bff;
    --modern-text: #333333;
    --modern-text-light: #666666;
    --modern-border: #e9ecef;

    --font-primary: 'Inter', sans-serif;
    --font-heading: 'Playfair Display', serif;

    --transition: all 0.3s ease;
    --border-radius: 8px;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--dark-color);
    overflow-x: hidden;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

img {
    max-width: 100%;
    height: auto;
}

/* Buttons */
.btn {
    font-weight: 500;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
}

.btn-primary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white-color);
}

.btn-primary:hover {
    background-color: #d35400;
    border-color: #d35400;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-outline-primary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

.btn-outline-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white-color);
}

.btn-outline-light {
    border-color: var(--white-color);
    color: var(--white-color);
}

.btn-outline-light:hover {
    background-color: var(--white-color);
    border-color: var(--white-color);
    color: var(--primary-color);
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--white-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.top-bar {
    background-color: var(--primary-color);
    color: var(--white-color);
    padding: 8px 0;
    font-size: 14px;
}

.contact-info .contact-item {
    color: var(--white-color);
    margin-right: 20px;
    font-size: 13px;
}

.contact-info .contact-item:hover {
    color: var(--secondary-color);
}

.contact-info .contact-item i {
    margin-right: 5px;
}

.social-links {
    text-align: right;
}

.social-link {
    color: var(--white-color);
    margin-left: 10px;
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--secondary-color);
    color: var(--white-color);
}

.navbar {
    padding: 15px 0;
}

.navbar-brand .logo {
    height: 50px;
    width: auto;
}

.navbar-nav .nav-link {
    color: var(--dark-color);
    font-weight: 500;
    margin: 0 10px;
    padding: 8px 0;
    position: relative;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 0.5px;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--secondary-color);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 100%;
}

/* Hero Section - Modern Design */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    color: var(--white-color);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
    z-index: -1;
}

/* Hero Top Bar */
.hero-top-bar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    padding: 25px 0;
    z-index: 10;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, transparent 100%);
}

.hero-location {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.location-text {
    font-size: 0.85rem;
    font-weight: 600;
    letter-spacing: 1.5px;
    opacity: 0.95;
    text-transform: uppercase;
}

.availability-text {
    font-size: 0.75rem;
    font-weight: 400;
    letter-spacing: 1px;
    opacity: 0.75;
    text-transform: uppercase;
}

.hero-nav {
    display: flex;
    align-items: center;
    gap: 35px;
}

.hero-nav-link {
    color: var(--white-color);
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 600;
    letter-spacing: 1.5px;
    transition: opacity 0.3s ease;
    text-transform: uppercase;
}

.hero-nav-link:hover {
    color: var(--white-color);
    opacity: 0.8;
}

.hero-nav-link .badge {
    background-color: rgba(255, 255, 255, 0.25);
    color: var(--white-color);
    font-size: 0.65rem;
    padding: 3px 8px;
    border-radius: 12px;
    margin-left: 8px;
    font-weight: 500;
}

.hero-cta {
    padding: 10px 25px;
    font-size: 0.8rem;
    font-weight: 700;
    letter-spacing: 1.5px;
    border-radius: 30px;
    border: 2px solid var(--white-color);
    background: var(--white-color);
    color: var(--dark-color);
    transition: all 0.3s ease;
    text-transform: uppercase;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero-cta:hover {
    background: transparent;
    color: var(--white-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Hero Main Content */
.hero-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 120px 0 80px 0;
    min-height: calc(100vh - 160px);
}

.hero-since {
    margin-bottom: 50px;
}

.since-text {
    font-size: 0.85rem;
    font-weight: 600;
    letter-spacing: 3px;
    opacity: 0.9;
    padding: 10px 25px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 30px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    text-transform: uppercase;
}

.hero-main-title {
    font-size: 5.5rem;
    font-weight: 900;
    line-height: 0.95;
    margin-bottom: 40px;
    letter-spacing: -2px;
    text-transform: uppercase;
    max-width: 1000px;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
    font-family: 'Inter', sans-serif;
}

.hero-main-subtitle {
    font-size: 1.1rem;
    font-weight: 400;
    line-height: 1.7;
    margin-bottom: 50px;
    opacity: 0.85;
    max-width: 550px;
    letter-spacing: 0.3px;
}

.hero-actions {
    display: flex;
    gap: 25px;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 20px;
}

.hero-btn-primary {
    padding: 18px 40px;
    font-size: 0.95rem;
    font-weight: 600;
    letter-spacing: 1px;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(255, 255, 255, 0.95);
    color: var(--dark-color);
    transition: all 0.4s ease;
    text-transform: uppercase;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.hero-btn-primary:hover {
    background: transparent;
    color: var(--white-color);
    border-color: var(--white-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.hero-btn-secondary {
    padding: 18px 40px;
    font-size: 0.95rem;
    font-weight: 600;
    letter-spacing: 1px;
    border-radius: 50px;
    border: 2px solid rgba(255, 255, 255, 0.6);
    background: transparent;
    color: var(--white-color);
    transition: all 0.4s ease;
    text-transform: uppercase;
}

.hero-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--white-color);
    transform: translateY(-2px);
}

/* Hero Bottom Section */
.hero-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 50px 0;
    z-index: 10;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, transparent 100%);
}

.hero-bottom-content {
    margin-bottom: 0;
}

.hero-bottom-title {
    font-size: 2.8rem;
    font-weight: 300;
    line-height: 1.2;
    margin: 0;
    letter-spacing: -0.5px;
}

.hero-bottom-title em {
    font-style: italic;
    font-weight: 400;
    opacity: 0.85;
}

.hero-scroll {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 15px;
}

.scroll-text {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 1.5px;
    opacity: 0.8;
    text-transform: uppercase;
}

.scroll-arrow {
    color: var(--white-color);
    font-size: 18px;
    opacity: 0.8;
    animation: bounce 2s infinite;
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateX(-50%) translateY(0);
    }

    40% {
        transform: translateX(-50%) translateY(-10px);
    }

    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Section Styles */
.section-header {
    margin-bottom: 3rem;
}

.section-subtitle {
    color: var(--secondary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 14px;
    display: block;
    margin-bottom: 10px;
}

.section-title {
    font-size: 2.5rem;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.1rem;
    color: var(--gray-color);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about-section {
    padding: 100px 0;
}

.about-images {
    position: relative;
}

.about-image-main {
    position: relative;
    z-index: 2;
}

.about-image-small {
    position: absolute;
    bottom: -30px;
    right: -30px;
    width: 200px;
    z-index: 3;
    border: 5px solid var(--white-color);
    border-radius: var(--border-radius);
}

.about-features {
    margin: 2rem 0;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.feature-content h5 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.feature-content p {
    color: var(--gray-color);
    margin-bottom: 0;
}

/* Services Section */
.services-section {
    padding: 100px 0;
}

.service-card {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.service-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(44, 62, 80, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.service-card:hover .service-overlay {
    opacity: 1;
}

.service-card:hover .service-image img {
    transform: scale(1.1);
}

.service-link {
    width: 60px;
    height: 60px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    transition: var(--transition);
}

.service-link:hover {
    background-color: var(--white-color);
    color: var(--secondary-color);
}

.service-content {
    padding: 2rem;
}

.service-title {
    margin-bottom: 1rem;
}

.service-title a {
    color: var(--dark-color);
}

.service-title a:hover {
    color: var(--secondary-color);
}

.service-description {
    color: var(--gray-color);
    margin-bottom: 1.5rem;
}

/* Projects Section */
.projects-section {
    padding: 100px 0;
}

.project-card {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    height: 300px;
    margin-bottom: 2rem;
}

.project-image {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.8), rgba(26, 37, 47, 0.6));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 2rem;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-info {
    color: var(--white-color);
}

.project-category {
    color: var(--secondary-color);
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.project-title {
    margin-top: 0.5rem;
    margin-bottom: 0;
}

.project-title a {
    color: var(--white-color);
}

.project-link {
    align-self: flex-end;
    width: 50px;
    height: 50px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.project-link:hover {
    background-color: var(--white-color);
    color: var(--secondary-color);
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
}

.cta-title {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: var(--white-color);
    padding: 60px 0 20px;
}

.footer-logo {
    height: 40px;
    width: auto;
}

.footer-description {
    color: var(--light-gray);
    margin-bottom: 1.5rem;
}

.footer-contact p {
    margin-bottom: 0.5rem;
    color: var(--light-gray);
}

.footer-contact a {
    color: var(--light-gray);
}

.footer-contact a:hover {
    color: var(--secondary-color);
}

.footer-title {
    color: var(--white-color);
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--light-gray);
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--secondary-color);
    padding-left: 5px;
}

.footer-social {
    margin-bottom: 1.5rem;
}

.footer-social .social-link {
    margin-right: 10px;
    margin-left: 0;
}

.newsletter-form .input-group {
    margin-bottom: 0;
}

.newsletter-form .form-control {
    border: none;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.newsletter-form .btn {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    padding: 12px 20px;
}

.footer-bottom {
    border-top: 1px solid #34495e;
    margin-top: 3rem;
    padding-top: 2rem;
}

.copyright {
    color: var(--light-gray);
    margin-bottom: 0;
}

.footer-bottom-links {
    text-align: right;
}

.footer-bottom-links a {
    color: var(--light-gray);
    margin-left: 20px;
}

.footer-bottom-links a:hover {
    color: var(--secondary-color);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border: none;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    z-index: 999;
}

.back-to-top:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.back-to-top.show {
    display: flex;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: var(--white-color);
    padding: 120px 0 80px;
    margin-top: 80px;
}

.page-header-content {
    text-align: center;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    justify-content: center;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
}

.breadcrumb-item.active {
    color: var(--white-color);
}

/* Projects Filter */
.projects-filter {
    border-bottom: 1px solid #dee2e6;
}

.filter-buttons .btn {
    margin: 0 5px 10px 0;
    border-radius: 25px;
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 500;
}

/* Project Info */
.project-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.project-info-item:last-child {
    border-bottom: none;
}

.project-info-item strong {
    color: var(--dark-color);
    font-weight: 600;
}

/* Gallery */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition);
}

/* Service Details */
.service-featured-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
}

.feature-item i {
    font-size: 16px;
}

/* Process Steps */
.process-step {
    padding: 20px;
}

.step-number {
    width: 50px;
    height: 50px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
    margin: 0 auto 15px;
}

.process-step h6 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 10px;
}

.process-step p {
    color: var(--gray-color);
    font-size: 14px;
    margin: 0;
}

/* Stats Section */
.stats-section {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
}

.stat-item {
    text-align: center;
    padding: 20px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--white-color);
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    color: var(--white-color);
}

/* Contact Page */
.contact-info-card {
    background: var(--white-color);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    height: 100%;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.contact-details h6 {
    margin-bottom: 5px;
    color: var(--dark-color);
    font-weight: 600;
}

.contact-details p {
    margin: 0;
    color: var(--gray-color);
}

.contact-details a {
    color: var(--gray-color);
}

.contact-details a:hover {
    color: var(--secondary-color);
}

.contact-form-card {
    background: var(--white-color);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.contact-form .form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 8px;
}

.contact-form .form-control {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    transition: border-color 0.3s ease;
}

.contact-form .form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(230, 126, 34, 0.25);
}

/* Map */
.map-container {
    position: relative;
    overflow: hidden;
}

.map-container iframe {
    width: 100%;
    height: 400px;
    border: none;
}

/* Business Hours */
.business-hours-card {
    background: var(--white-color);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.hours-item {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.hours-item:last-child {
    border-bottom: none;
}

.hours-item strong {
    color: var(--dark-color);
    display: block;
    margin-bottom: 5px;
}

.hours-item p {
    margin: 0;
    color: var(--gray-color);
}

/* About Page */
.about-detail-section {
    padding: 100px 0;
}

.stat-item {
    text-align: center;
    padding: 30px 20px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--gray-color);
    font-weight: 500;
}

/* Mission & Vision */
.mission-vision-section {
    padding: 100px 0;
}

.mission-card,
.vision-card {
    background: var(--white-color);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    height: 100%;
    text-align: center;
}

.card-icon {
    width: 80px;
    height: 80px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 32px;
}

.mission-card h3,
.vision-card h3 {
    color: var(--dark-color);
    margin-bottom: 20px;
}

/* Values */
.values-section {
    padding: 100px 0;
}

.value-item {
    text-align: center;
    padding: 30px 20px;
}

.value-icon {
    width: 70px;
    height: 70px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 28px;
}

.value-item h4 {
    color: var(--dark-color);
    margin-bottom: 15px;
}

.value-item p {
    color: var(--gray-color);
    margin: 0;
}

/* Team */
.team-section {
    padding: 100px 0;
}

.team-member {
    text-align: center;
    background: var(--white-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.team-member:hover {
    transform: translateY(-10px);
}

.member-image {
    position: relative;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: var(--transition);
}

.member-social {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: var(--transition);
}

.team-member:hover .member-social {
    opacity: 1;
}

.team-member:hover .member-image img {
    transform: scale(1.1);
}

.member-social .social-link {
    margin: 0 5px;
    background-color: var(--white-color);
    color: var(--primary-color);
}

.member-info {
    padding: 25px;
}

.member-info h5 {
    color: var(--dark-color);
    margin-bottom: 5px;
}

.member-info p {
    color: var(--gray-color);
    margin: 0;
}

/* Service Detail Cards */
.service-detail-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
    transition: var(--transition);
}

.service-detail-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.service-detail-card .service-image {
    margin-bottom: 0;
}

.service-detail-card .service-image img {
    height: 200px;
    object-fit: cover;
}

.service-detail-card .service-content {
    padding: 0;
    padding-left: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {

    /* Hero Mobile Styles */
    .hero-top-bar {
        padding: 15px 0;
    }

    .hero-location {
        margin-bottom: 15px;
    }

    .hero-nav {
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .hero-nav-link {
        font-size: 0.8rem;
    }

    .hero-cta {
        padding: 6px 15px;
        font-size: 0.8rem;
    }

    .hero-main-title {
        font-size: 3rem;
        line-height: 1.1;
        margin-bottom: 25px;
        letter-spacing: -1px;
    }

    .hero-main-subtitle {
        font-size: 1rem;
        margin-bottom: 35px;
        max-width: 90%;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: 20px;
        margin-top: 15px;
    }

    .hero-btn-primary,
    .hero-btn-secondary {
        padding: 15px 35px;
        font-size: 0.85rem;
        width: 100%;
        max-width: 280px;
        letter-spacing: 0.8px;
    }

    .hero-bottom {
        padding: 30px 0;
    }

    .hero-bottom-title {
        font-size: 2rem;
        margin-bottom: 20px;
        text-align: center;
    }

    .hero-scroll {
        align-items: center;
        margin-top: 20px;
    }

    .hero-since {
        margin-bottom: 35px;
    }

    .since-text {
        font-size: 0.8rem;
        padding: 8px 20px;
        letter-spacing: 2px;
    }

    .section-title {
        font-size: 2rem;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .about-image-small {
        position: static;
        width: 100%;
        margin-top: 20px;
    }

    .social-links {
        text-align: left;
        margin-top: 10px;
    }

    .footer-bottom-links {
        text-align: left;
        margin-top: 10px;
    }

    .footer-bottom-links a {
        margin-left: 0;
        margin-right: 20px;
    }

    .service-detail-card .service-content {
        padding-left: 0;
        padding-top: 20px;
    }

    .contact-info-card,
    .contact-form-card {
        margin-bottom: 30px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .mission-card,
    .vision-card {
        margin-bottom: 30px;
    }
}

/* ===== MODERN DESIGN STYLES ===== */

/* Modern Header Styles */
.modern-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--white-color);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.top-contact-bar {
    background-color: var(--modern-light);
    padding: 12px 0;
    border-bottom: 1px solid var(--modern-border);
}

.contact-info-modern {
    display: flex;
    gap: 30px;
}

.contact-item-modern {
    font-size: 14px;
    color: var(--modern-text);
}

.contact-item-modern strong {
    font-weight: 600;
    margin-right: 8px;
}

.contact-item-modern a {
    color: var(--modern-text);
    text-decoration: none;
    transition: var(--transition);
}

.contact-item-modern a:hover {
    color: var(--modern-accent);
}

.header-logo-small .logo-small {
    height: 30px;
    width: auto;
}

.modern-navbar {
    padding: 20px 0;
    background-color: var(--white-color);
}

.navbar-brand-modern .logo-main {
    height: 60px;
    width: auto;
}

.modern-nav .modern-nav-link {
    color: var(--modern-text);
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0 15px;
    padding: 8px 0;
    position: relative;
    transition: var(--transition);
}

.modern-nav .modern-nav-link:hover,
.modern-nav .modern-nav-link.active {
    color: var(--modern-accent);
}

.modern-nav .modern-nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--modern-accent);
    transition: var(--transition);
}

.modern-nav .modern-nav-link:hover::after,
.modern-nav .modern-nav-link.active::after {
    width: 100%;
}

.btn-modern-cta {
    background-color: var(--modern-accent);
    color: var(--white-color);
    padding: 10px 25px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: none;
    transition: var(--transition);
}

.btn-modern-cta:hover {
    background-color: #0056b3;
    color: var(--white-color);
    transform: translateY(-2px);
}

.modern-dropdown {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 10px 0;
}

.modern-dropdown .dropdown-item {
    padding: 10px 20px;
    font-size: 14px;
    color: var(--modern-text);
    transition: var(--transition);
}

.modern-dropdown .dropdown-item:hover {
    background-color: var(--modern-light);
    color: var(--modern-accent);
}

/* Modern Hero Section */
.modern-hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    color: var(--white-color);
    overflow: hidden;
    margin-top: 120px;
}

.hero-background-modern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-bg-modern {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay-modern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
    z-index: -1;
}

.hero-content-modern {
    z-index: 10;
    padding: 60px 0;
}

.experience-badge {
    margin-bottom: 30px;
}

.experience-title {
    font-size: 3.5rem;
    font-weight: 900;
    color: var(--white-color);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle-modern {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    line-height: 1.6;
    max-width: 600px;
}

.passion-statement {
    margin-top: 40px;
}

.passion-title {
    font-size: 4rem;
    font-weight: 300;
    line-height: 1.2;
    margin-bottom: 40px;
    color: var(--white-color);
}

.passion-title em {
    font-style: italic;
    font-weight: 400;
    color: #ffd700;
}

.video-play-section {
    margin-top: 40px;
}

.play-button {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 50px;
    padding: 15px 30px;
    color: var(--white-color);
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 10px;
}

.play-button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: var(--white-color);
    transform: translateY(-2px);
}

.play-button i {
    font-size: 18px;
}

/* Philosophy Section */
.philosophy-section {
    padding: 100px 0;
    background-color: var(--white-color);
}

.section-label {
    color: var(--modern-text-light);
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px;
    display: block;
}

.philosophy-title {
    font-size: 3rem;
    font-weight: 300;
    color: var(--modern-text);
    margin-bottom: 30px;
    line-height: 1.2;
}

.philosophy-title .highlight {
    font-style: italic;
    color: var(--modern-accent);
}

.philosophy-description {
    font-size: 16px;
    color: var(--modern-text-light);
    line-height: 1.7;
    margin-bottom: 20px;
}

.philosophy-actions {
    margin-top: 40px;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn-modern-primary {
    background-color: var(--modern-accent);
    color: var(--white-color);
    padding: 12px 30px;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
    border: none;
}

.btn-modern-primary:hover {
    background-color: #0056b3;
    color: var(--white-color);
    transform: translateY(-2px);
}

.btn-modern-outline {
    background-color: transparent;
    color: var(--modern-text);
    padding: 12px 30px;
    border: 2px solid var(--modern-border);
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
}

.btn-modern-outline:hover {
    background-color: var(--modern-accent);
    color: var(--white-color);
    border-color: var(--modern-accent);
}

.philosophy-image {
    position: relative;
}

.stats-overlay {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-item-overlay {
    margin-bottom: 20px;
}

.stat-number-overlay {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--modern-accent);
    margin: 0;
}

.stat-label-overlay {
    font-size: 14px;
    color: var(--modern-text);
    margin: 5px 0;
}

.stat-badge {
    background-color: var(--modern-accent);
    color: var(--white-color);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.process-steps {
    display: flex;
    gap: 15px;
}

.step {
    background-color: var(--modern-light);
    color: var(--modern-text);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

/* Modern Services Section */
.modern-services-section {
    padding: 100px 0;
    background-color: var(--modern-light);
}

.services-header {
    margin-bottom: 50px;
}

.services-title {
    font-size: 2.5rem;
    font-weight: 300;
    color: var(--modern-text);
    margin-bottom: 20px;
}

.services-description {
    font-size: 16px;
    color: var(--modern-text-light);
    line-height: 1.7;
    margin-bottom: 30px;
}

.services-nav {
    display: flex;
    gap: 30px;
}

.services-nav-link {
    color: var(--modern-text);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: var(--transition);
}

.services-nav-link:hover {
    color: var(--modern-accent);
}

.services-nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--modern-accent);
    transition: var(--transition);
}

.services-nav-link:hover::after {
    width: 100%;
}

.modern-service-card {
    background: var(--white-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    height: 100%;
}

.modern-service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.service-image-modern {
    height: 200px;
    overflow: hidden;
}

.service-image-modern img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.modern-service-card:hover .service-image-modern img {
    transform: scale(1.1);
}

.service-content-modern {
    padding: 30px;
    position: relative;
}

.service-icon-modern {
    width: 60px;
    height: 60px;
    background-color: var(--modern-accent);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 20px;
}

.service-title-modern {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--modern-text);
    margin-bottom: 15px;
}

.service-description-modern {
    color: var(--modern-text-light);
    line-height: 1.6;
    margin-bottom: 20px;
}

.service-badge-modern {
    background-color: var(--modern-light);
    color: var(--modern-text);
    padding: 6px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 20px;
}

.service-explore-btn {
    color: var(--modern-accent);
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

.service-explore-btn:hover {
    color: #0056b3;
}

/* Modern Projects Section */
.modern-projects-section {
    padding: 100px 0;
    background-color: var(--white-color);
}

.section-header-modern {
    margin-bottom: 60px;
}

.section-title-modern {
    font-size: 2.5rem;
    font-weight: 300;
    color: var(--modern-text);
    margin-bottom: 20px;
}

.section-description-modern {
    font-size: 16px;
    color: var(--modern-text-light);
    line-height: 1.7;
    max-width: 600px;
    margin: 0 auto;
}

.modern-project-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    height: 350px;
    margin-bottom: 30px;
}

.project-image-modern {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.project-image-modern img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.project-overlay-modern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 30px;
    opacity: 0;
    transition: var(--transition);
}

.modern-project-card:hover .project-overlay-modern {
    opacity: 1;
}

.modern-project-card:hover .project-image-modern img {
    transform: scale(1.1);
}

.project-status {
    background-color: var(--modern-accent);
    color: var(--white-color);
    padding: 6px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    align-self: flex-start;
}

.project-info-modern {
    color: var(--white-color);
}

.project-title-modern {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.project-subtitle {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
}

.project-view-btn {
    background-color: var(--white-color);
    color: var(--modern-text);
    padding: 10px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: var(--transition);
    display: inline-block;
}

.project-view-btn:hover {
    background-color: var(--modern-accent);
    color: var(--white-color);
}

/* Modern Testimonials Section */
.modern-testimonials-section {
    padding: 100px 0;
    background-color: var(--modern-light);
}

.testimonial-card-modern {
    background: var(--white-color);
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

.testimonial-text {
    font-size: 18px;
    color: var(--modern-text);
    line-height: 1.7;
    margin-bottom: 30px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.author-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--modern-text);
    margin: 0;
}

.author-company {
    color: var(--modern-text-light);
    margin: 5px 0;
}

.author-verification {
    display: flex;
    align-items: center;
    gap: 15px;
}

.verified-badge {
    background-color: #28a745;
    color: var(--white-color);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.rating {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rating-score {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--modern-text);
}

.stars {
    color: #ffc107;
}

.testimonial-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    padding: 30px;
    background: var(--white-color);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.stat-item-testimonial {
    text-align: center;
}

.stat-item-testimonial .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--modern-accent);
    display: block;
}

.stat-item-testimonial .stat-label {
    font-size: 14px;
    color: var(--modern-text-light);
    margin-top: 5px;
}

.stat-item-testimonial .stat-text {
    font-size: 14px;
    color: var(--modern-text-light);
}

/* Modern Footer */
.modern-footer {
    background-color: var(--modern-dark);
    color: var(--white-color);
    padding: 80px 0 40px;
}

.footer-brand {
    margin-bottom: 40px;
}

.footer-logo-modern {
    height: 50px;
    width: auto;
    margin-bottom: 30px;
}

.footer-nav-links {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.footer-nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.footer-nav-link:hover {
    color: var(--white-color);
}

.btn-footer-cta {
    background-color: var(--modern-accent);
    color: var(--white-color);
    padding: 12px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    display: inline-block;
}

.btn-footer-cta:hover {
    background-color: #0056b3;
    color: var(--white-color);
}

.footer-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-email {
    color: var(--white-color);
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    transition: var(--transition);
}

.footer-email:hover {
    color: var(--modern-accent);
}

.footer-contact-modern {
    margin-bottom: 40px;
}

.contact-item-footer {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

.contact-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.contact-value {
    color: var(--white-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.contact-value:hover {
    color: var(--modern-accent);
}

.footer-nav-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--white-color);
}

.footer-nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-nav-list li {
    margin-bottom: 10px;
}

.footer-nav-list a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-nav-list a:hover {
    color: var(--white-color);
    padding-left: 5px;
}

.footer-bottom-modern {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 60px;
    padding-top: 40px;
}

.footer-brand-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white-color);
    margin: 0;
}

.footer-legal {
    text-align: right;
}

.copyright-modern {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin-bottom: 10px;
    display: block;
}

.legal-links {
    display: flex;
    gap: 20px;
    justify-content: flex-end;
}

.legal-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 14px;
    transition: var(--transition);
}

.legal-links a:hover {
    color: var(--white-color);
}

/* Modern Design Responsive Styles */
@media (max-width: 768px) {
    /* Modern Header Mobile */
    .contact-info-modern {
        flex-direction: column;
        gap: 10px;
    }

    .header-logo-small {
        text-align: center;
        margin-top: 10px;
    }

    .modern-nav .modern-nav-link {
        margin: 0 5px;
        font-size: 13px;
    }

    .btn-modern-cta {
        padding: 8px 20px;
        font-size: 13px;
    }

    /* Modern Hero Mobile */
    .modern-hero {
        margin-top: 140px;
        min-height: 80vh;
    }

    .experience-title {
        font-size: 2.5rem;
    }

    .passion-title {
        font-size: 2.5rem;
    }

    .hero-subtitle-modern {
        font-size: 1rem;
    }

    .play-button {
        padding: 12px 25px;
        font-size: 14px;
    }

    /* Philosophy Section Mobile */
    .philosophy-title {
        font-size: 2rem;
    }

    .philosophy-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-modern-primary,
    .btn-modern-outline {
        text-align: center;
        margin-bottom: 10px;
    }

    .stats-overlay {
        position: static;
        margin-top: 20px;
        width: 100%;
    }

    .process-steps {
        justify-content: center;
    }

    /* Services Mobile */
    .services-nav {
        flex-direction: column;
        gap: 15px;
    }

    .services-title {
        font-size: 2rem;
    }

    /* Projects Mobile */
    .section-title-modern {
        font-size: 2rem;
    }

    .modern-project-card {
        height: 300px;
    }

    /* Testimonials Mobile */
    .testimonial-author {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .author-verification {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .testimonial-stats {
        flex-direction: column;
        text-align: center;
    }

    /* Footer Mobile */
    .footer-nav-links {
        flex-direction: column;
        gap: 15px;
    }

    .footer-legal {
        text-align: left;
        margin-top: 20px;
    }

    .legal-links {
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .copyright-modern {
        margin-bottom: 15px;
    }

    .footer-brand-name {
        font-size: 1.2rem;
        margin-bottom: 20px;
    }
}