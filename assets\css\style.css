/* Flori Construction Ltd - Main Stylesheet */

/* CSS Variables */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #e67e22;
    --accent-color: #3498db;
    --dark-color: #1a252f;
    --light-color: #ecf0f1;
    --white-color: #ffffff;
    --gray-color: #7f8c8d;
    --light-gray: #bdc3c7;

    --font-primary: 'Inter', sans-serif;
    --font-heading: 'Playfair Display', serif;

    --transition: all 0.3s ease;
    --border-radius: 8px;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--dark-color);
    overflow-x: hidden;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

img {
    max-width: 100%;
    height: auto;
}

/* Buttons */
.btn {
    font-weight: 500;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
}

.btn-primary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white-color);
}

.btn-primary:hover {
    background-color: #d35400;
    border-color: #d35400;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-outline-primary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

.btn-outline-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white-color);
}

.btn-outline-light {
    border-color: var(--white-color);
    color: var(--white-color);
}

.btn-outline-light:hover {
    background-color: var(--white-color);
    border-color: var(--white-color);
    color: var(--primary-color);
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--white-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.top-bar {
    background-color: var(--primary-color);
    color: var(--white-color);
    padding: 8px 0;
    font-size: 14px;
}

.contact-info .contact-item {
    color: var(--white-color);
    margin-right: 20px;
    font-size: 13px;
}

.contact-info .contact-item:hover {
    color: var(--secondary-color);
}

.contact-info .contact-item i {
    margin-right: 5px;
}

.social-links {
    text-align: right;
}

.social-link {
    color: var(--white-color);
    margin-left: 10px;
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--secondary-color);
    color: var(--white-color);
}

.navbar {
    padding: 15px 0;
}

.navbar-brand .logo {
    height: 50px;
    width: auto;
}

.navbar-nav .nav-link {
    color: var(--dark-color);
    font-weight: 500;
    margin: 0 10px;
    padding: 8px 0;
    position: relative;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 0.5px;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--secondary-color);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 100%;
}

/* Hero Section - Modern Design */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    color: var(--white-color);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
    z-index: -1;
}

/* Hero Top Bar */
.hero-top-bar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    padding: 25px 0;
    z-index: 10;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, transparent 100%);
}

.hero-location {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.location-text {
    font-size: 0.85rem;
    font-weight: 600;
    letter-spacing: 1.5px;
    opacity: 0.95;
    text-transform: uppercase;
}

.availability-text {
    font-size: 0.75rem;
    font-weight: 400;
    letter-spacing: 1px;
    opacity: 0.75;
    text-transform: uppercase;
}

.hero-nav {
    display: flex;
    align-items: center;
    gap: 35px;
}

.hero-nav-link {
    color: var(--white-color);
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 600;
    letter-spacing: 1.5px;
    transition: opacity 0.3s ease;
    text-transform: uppercase;
}

.hero-nav-link:hover {
    color: var(--white-color);
    opacity: 0.8;
}

.hero-nav-link .badge {
    background-color: rgba(255, 255, 255, 0.25);
    color: var(--white-color);
    font-size: 0.65rem;
    padding: 3px 8px;
    border-radius: 12px;
    margin-left: 8px;
    font-weight: 500;
}

.hero-cta {
    padding: 10px 25px;
    font-size: 0.8rem;
    font-weight: 700;
    letter-spacing: 1.5px;
    border-radius: 30px;
    border: 2px solid var(--white-color);
    background: var(--white-color);
    color: var(--dark-color);
    transition: all 0.3s ease;
    text-transform: uppercase;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero-cta:hover {
    background: transparent;
    color: var(--white-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Hero Main Content */
.hero-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 120px 0 80px 0;
    min-height: calc(100vh - 160px);
}

.hero-since {
    margin-bottom: 50px;
}

.since-text {
    font-size: 0.85rem;
    font-weight: 600;
    letter-spacing: 3px;
    opacity: 0.9;
    padding: 10px 25px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 30px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    text-transform: uppercase;
}

.hero-main-title {
    font-size: 5.5rem;
    font-weight: 900;
    line-height: 0.95;
    margin-bottom: 40px;
    letter-spacing: -2px;
    text-transform: uppercase;
    max-width: 1000px;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
    font-family: 'Inter', sans-serif;
}

.hero-main-subtitle {
    font-size: 1.1rem;
    font-weight: 400;
    line-height: 1.7;
    margin-bottom: 50px;
    opacity: 0.85;
    max-width: 550px;
    letter-spacing: 0.3px;
}

.hero-actions {
    display: flex;
    gap: 25px;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 20px;
}

.hero-btn-primary {
    padding: 18px 40px;
    font-size: 0.95rem;
    font-weight: 600;
    letter-spacing: 1px;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(255, 255, 255, 0.95);
    color: var(--dark-color);
    transition: all 0.4s ease;
    text-transform: uppercase;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.hero-btn-primary:hover {
    background: transparent;
    color: var(--white-color);
    border-color: var(--white-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.hero-btn-secondary {
    padding: 18px 40px;
    font-size: 0.95rem;
    font-weight: 600;
    letter-spacing: 1px;
    border-radius: 50px;
    border: 2px solid rgba(255, 255, 255, 0.6);
    background: transparent;
    color: var(--white-color);
    transition: all 0.4s ease;
    text-transform: uppercase;
}

.hero-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--white-color);
    transform: translateY(-2px);
}

/* Hero Bottom Section */
.hero-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 50px 0;
    z-index: 10;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, transparent 100%);
}

.hero-bottom-content {
    margin-bottom: 0;
}

.hero-bottom-title {
    font-size: 2.8rem;
    font-weight: 300;
    line-height: 1.2;
    margin: 0;
    letter-spacing: -0.5px;
}

.hero-bottom-title em {
    font-style: italic;
    font-weight: 400;
    opacity: 0.85;
}

.hero-scroll {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 15px;
}

.scroll-text {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 1.5px;
    opacity: 0.8;
    text-transform: uppercase;
}

.scroll-arrow {
    color: var(--white-color);
    font-size: 18px;
    opacity: 0.8;
    animation: bounce 2s infinite;
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateX(-50%) translateY(0);
    }

    40% {
        transform: translateX(-50%) translateY(-10px);
    }

    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Section Styles */
.section-header {
    margin-bottom: 3rem;
}

.section-subtitle {
    color: var(--secondary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 14px;
    display: block;
    margin-bottom: 10px;
}

.section-title {
    font-size: 2.5rem;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.1rem;
    color: var(--gray-color);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about-section {
    padding: 100px 0;
}

.about-images {
    position: relative;
}

.about-image-main {
    position: relative;
    z-index: 2;
}

.about-image-small {
    position: absolute;
    bottom: -30px;
    right: -30px;
    width: 200px;
    z-index: 3;
    border: 5px solid var(--white-color);
    border-radius: var(--border-radius);
}

.about-features {
    margin: 2rem 0;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.feature-content h5 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.feature-content p {
    color: var(--gray-color);
    margin-bottom: 0;
}

/* Services Section */
.services-section {
    padding: 100px 0;
}

.service-card {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.service-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(44, 62, 80, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.service-card:hover .service-overlay {
    opacity: 1;
}

.service-card:hover .service-image img {
    transform: scale(1.1);
}

.service-link {
    width: 60px;
    height: 60px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    transition: var(--transition);
}

.service-link:hover {
    background-color: var(--white-color);
    color: var(--secondary-color);
}

.service-content {
    padding: 2rem;
}

.service-title {
    margin-bottom: 1rem;
}

.service-title a {
    color: var(--dark-color);
}

.service-title a:hover {
    color: var(--secondary-color);
}

.service-description {
    color: var(--gray-color);
    margin-bottom: 1.5rem;
}

/* Projects Section */
.projects-section {
    padding: 100px 0;
}

.project-card {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    height: 300px;
    margin-bottom: 2rem;
}

.project-image {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.8), rgba(26, 37, 47, 0.6));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 2rem;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-info {
    color: var(--white-color);
}

.project-category {
    color: var(--secondary-color);
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.project-title {
    margin-top: 0.5rem;
    margin-bottom: 0;
}

.project-title a {
    color: var(--white-color);
}

.project-link {
    align-self: flex-end;
    width: 50px;
    height: 50px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.project-link:hover {
    background-color: var(--white-color);
    color: var(--secondary-color);
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
}

.cta-title {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: var(--white-color);
    padding: 60px 0 20px;
}

.footer-logo {
    height: 40px;
    width: auto;
}

.footer-description {
    color: var(--light-gray);
    margin-bottom: 1.5rem;
}

.footer-contact p {
    margin-bottom: 0.5rem;
    color: var(--light-gray);
}

.footer-contact a {
    color: var(--light-gray);
}

.footer-contact a:hover {
    color: var(--secondary-color);
}

.footer-title {
    color: var(--white-color);
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--light-gray);
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--secondary-color);
    padding-left: 5px;
}

.footer-social {
    margin-bottom: 1.5rem;
}

.footer-social .social-link {
    margin-right: 10px;
    margin-left: 0;
}

.newsletter-form .input-group {
    margin-bottom: 0;
}

.newsletter-form .form-control {
    border: none;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.newsletter-form .btn {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    padding: 12px 20px;
}

.footer-bottom {
    border-top: 1px solid #34495e;
    margin-top: 3rem;
    padding-top: 2rem;
}

.copyright {
    color: var(--light-gray);
    margin-bottom: 0;
}

.footer-bottom-links {
    text-align: right;
}

.footer-bottom-links a {
    color: var(--light-gray);
    margin-left: 20px;
}

.footer-bottom-links a:hover {
    color: var(--secondary-color);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border: none;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    z-index: 999;
}

.back-to-top:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.back-to-top.show {
    display: flex;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: var(--white-color);
    padding: 120px 0 80px;
    margin-top: 80px;
}

.page-header-content {
    text-align: center;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    justify-content: center;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
}

.breadcrumb-item.active {
    color: var(--white-color);
}

/* Projects Filter */
.projects-filter {
    border-bottom: 1px solid #dee2e6;
}

.filter-buttons .btn {
    margin: 0 5px 10px 0;
    border-radius: 25px;
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 500;
}

/* Project Info */
.project-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.project-info-item:last-child {
    border-bottom: none;
}

.project-info-item strong {
    color: var(--dark-color);
    font-weight: 600;
}

/* Gallery */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition);
}

/* Service Details */
.service-featured-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
}

.feature-item i {
    font-size: 16px;
}

/* Process Steps */
.process-step {
    padding: 20px;
}

.step-number {
    width: 50px;
    height: 50px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
    margin: 0 auto 15px;
}

.process-step h6 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 10px;
}

.process-step p {
    color: var(--gray-color);
    font-size: 14px;
    margin: 0;
}

/* Stats Section */
.stats-section {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
}

.stat-item {
    text-align: center;
    padding: 20px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--white-color);
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    color: var(--white-color);
}

/* Contact Page */
.contact-info-card {
    background: var(--white-color);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    height: 100%;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.contact-details h6 {
    margin-bottom: 5px;
    color: var(--dark-color);
    font-weight: 600;
}

.contact-details p {
    margin: 0;
    color: var(--gray-color);
}

.contact-details a {
    color: var(--gray-color);
}

.contact-details a:hover {
    color: var(--secondary-color);
}

.contact-form-card {
    background: var(--white-color);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.contact-form .form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 8px;
}

.contact-form .form-control {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    transition: border-color 0.3s ease;
}

.contact-form .form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(230, 126, 34, 0.25);
}

/* Map */
.map-container {
    position: relative;
    overflow: hidden;
}

.map-container iframe {
    width: 100%;
    height: 400px;
    border: none;
}

/* Business Hours */
.business-hours-card {
    background: var(--white-color);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.hours-item {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.hours-item:last-child {
    border-bottom: none;
}

.hours-item strong {
    color: var(--dark-color);
    display: block;
    margin-bottom: 5px;
}

.hours-item p {
    margin: 0;
    color: var(--gray-color);
}

/* About Page */
.about-detail-section {
    padding: 100px 0;
}

.stat-item {
    text-align: center;
    padding: 30px 20px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--gray-color);
    font-weight: 500;
}

/* Mission & Vision */
.mission-vision-section {
    padding: 100px 0;
}

.mission-card,
.vision-card {
    background: var(--white-color);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    height: 100%;
    text-align: center;
}

.card-icon {
    width: 80px;
    height: 80px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 32px;
}

.mission-card h3,
.vision-card h3 {
    color: var(--dark-color);
    margin-bottom: 20px;
}

/* Values */
.values-section {
    padding: 100px 0;
}

.value-item {
    text-align: center;
    padding: 30px 20px;
}

.value-icon {
    width: 70px;
    height: 70px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 28px;
}

.value-item h4 {
    color: var(--dark-color);
    margin-bottom: 15px;
}

.value-item p {
    color: var(--gray-color);
    margin: 0;
}

/* Team */
.team-section {
    padding: 100px 0;
}

.team-member {
    text-align: center;
    background: var(--white-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.team-member:hover {
    transform: translateY(-10px);
}

.member-image {
    position: relative;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: var(--transition);
}

.member-social {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: var(--transition);
}

.team-member:hover .member-social {
    opacity: 1;
}

.team-member:hover .member-image img {
    transform: scale(1.1);
}

.member-social .social-link {
    margin: 0 5px;
    background-color: var(--white-color);
    color: var(--primary-color);
}

.member-info {
    padding: 25px;
}

.member-info h5 {
    color: var(--dark-color);
    margin-bottom: 5px;
}

.member-info p {
    color: var(--gray-color);
    margin: 0;
}

/* Service Detail Cards */
.service-detail-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
    transition: var(--transition);
}

.service-detail-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.service-detail-card .service-image {
    margin-bottom: 0;
}

.service-detail-card .service-image img {
    height: 200px;
    object-fit: cover;
}

.service-detail-card .service-content {
    padding: 0;
    padding-left: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {

    /* Hero Mobile Styles */
    .hero-top-bar {
        padding: 15px 0;
    }

    .hero-location {
        margin-bottom: 15px;
    }

    .hero-nav {
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .hero-nav-link {
        font-size: 0.8rem;
    }

    .hero-cta {
        padding: 6px 15px;
        font-size: 0.8rem;
    }

    .hero-main-title {
        font-size: 3rem;
        line-height: 1.1;
        margin-bottom: 25px;
        letter-spacing: -1px;
    }

    .hero-main-subtitle {
        font-size: 1rem;
        margin-bottom: 35px;
        max-width: 90%;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: 20px;
        margin-top: 15px;
    }

    .hero-btn-primary,
    .hero-btn-secondary {
        padding: 15px 35px;
        font-size: 0.85rem;
        width: 100%;
        max-width: 280px;
        letter-spacing: 0.8px;
    }

    .hero-bottom {
        padding: 30px 0;
    }

    .hero-bottom-title {
        font-size: 2rem;
        margin-bottom: 20px;
        text-align: center;
    }

    .hero-scroll {
        align-items: center;
        margin-top: 20px;
    }

    .hero-since {
        margin-bottom: 35px;
    }

    .since-text {
        font-size: 0.8rem;
        padding: 8px 20px;
        letter-spacing: 2px;
    }

    .section-title {
        font-size: 2rem;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .about-image-small {
        position: static;
        width: 100%;
        margin-top: 20px;
    }

    .social-links {
        text-align: left;
        margin-top: 10px;
    }

    .footer-bottom-links {
        text-align: left;
        margin-top: 10px;
    }

    .footer-bottom-links a {
        margin-left: 0;
        margin-right: 20px;
    }

    .service-detail-card .service-content {
        padding-left: 0;
        padding-top: 20px;
    }

    .contact-info-card,
    .contact-form-card {
        margin-bottom: 30px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .mission-card,
    .vision-card {
        margin-bottom: 30px;
    }
}